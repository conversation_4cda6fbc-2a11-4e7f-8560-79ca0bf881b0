<svg width="400" height="240" viewBox="0 0 400 240" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="240" fill="url(#gradient1)"/>
  <!-- Chart Bars -->
  <rect x="60" y="180" width="30" height="40" fill="#FFFFFF" opacity="0.9"/>
  <rect x="100" y="160" width="30" height="60" fill="#FFFFFF" opacity="0.9"/>
  <rect x="140" y="140" width="30" height="80" fill="#FFFFFF" opacity="0.9"/>
  <rect x="180" y="120" width="30" height="100" fill="#FFFFFF" opacity="0.9"/>
  <rect x="220" y="100" width="30" height="120" fill="#FFFFFF" opacity="0.9"/>
  <!-- Line Chart -->
  <path d="M300 180L320 160L340 120L360 100" stroke="#FFFFFF" stroke-width="3" fill="none" opacity="0.9"/>
  <circle cx="300" cy="180" r="4" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="320" cy="160" r="4" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="340" cy="120" r="4" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="360" cy="100" r="4" fill="#FFFFFF" opacity="0.9"/>
  <!-- Title -->
  <text x="200" y="40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Data Analytics</text>
  <text x="200" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">Visualization &amp; Insights</text>
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
